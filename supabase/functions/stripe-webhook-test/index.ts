import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

/**
 * Transforms a hex string into a Uint8Array
 * @param hex a hex string to turn into Uint8Array
 * @returns Uint8Array
 */
function hexToUint8Array(hex: string): Uint8Array {
  const bytes = new Uint8Array(Math.ceil(hex.length / 2));
  for (let i = 0; i < bytes.length; i++) {
    bytes[i] = parseInt(hex.substring(i * 2, i * 2 + 2), 16);
  }
  return bytes;
}

/**
 * Imports a webhook secret as a crypto key for HMAC verification
 * @param secret webhook secret used to import the key
 * @returns CryptoKey for HMAC verification
 */
async function importKey(secret: string): Promise<CryptoKey> {
  return await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(secret),
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["verify"]
  );
}

/**
 * Verifies a Stripe webhook signature using Web Crypto API
 * @param payload the request payload (raw body)
 * @param signature the Stripe-Signature header value
 * @param webhookSecret the webhook secret from Stripe
 * @returns boolean indicating if signature is valid
 */
async function verifyStripeSignature(
  payload: string,
  signature: string,
  webhookSecret: string
): Promise<boolean> {
  // Parse the signature header
  const elements = signature.split(',');
  const parts: Record<string, string> = {};

  elements.forEach((element: string) => {
    const elementParts = element.split('=');
    parts[elementParts[0]] = elementParts[1];
  });

  const timestamp = parts.t;
  const v1Signature = parts.v1;

  if (!timestamp || !v1Signature) {
    throw new Error('Invalid signature format');
  }

  console.log(`Timestamp: ${timestamp}`);
  console.log(`v1 Signature: ${v1Signature}`);

  // Create the signed payload (timestamp.payload)
  const signedPayload = `${timestamp}.${payload}`;
  console.log(`Signed payload: ${signedPayload.substring(0, 100)}...`);

  // Import the webhook secret as a crypto key
  const key = await importKey(webhookSecret);

  // Verify the signature using Web Crypto API
  const verified = await crypto.subtle.verify(
    "HMAC",
    key,
    hexToUint8Array(v1Signature), // Convert hex signature to Uint8Array
    new TextEncoder().encode(signedPayload) // Encode the signed payload
  );

  // Check timestamp tolerance (5 minutes)
  const elapsed = Math.floor(Date.now() / 1000) - Number(timestamp);
  const tolerance = 300; // 5 minutes in seconds

  console.log(`Time elapsed: ${elapsed} seconds (tolerance: ${tolerance})`);

  if (tolerance && elapsed > tolerance) {
    throw new Error(`Timestamp too old: ${elapsed} seconds elapsed`);
  }

  return verified;
}

serve(async (req) => {
  console.log("=== STRIPE WEBHOOK TEST FUNCTION ===");
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // 1. Log the STRIPE_WEBHOOK_SECRET environment variable (masked for security)
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    console.log(`STRIPE_WEBHOOK_SECRET configured: ${webhookSecret ? "YES (value exists)" : "NO (missing)"}`);
    if (webhookSecret) {
      // Only show first and last few characters for security
      const maskedSecret = webhookSecret.length > 8 
        ? `${webhookSecret.substring(0, 4)}...${webhookSecret.substring(webhookSecret.length - 4)}`
        : "(too short to mask safely)";
      console.log(`STRIPE_WEBHOOK_SECRET format: ${maskedSecret}`);
      console.log(`STRIPE_WEBHOOK_SECRET length: ${webhookSecret.length} characters`);
    }

    // 2. Log the incoming webhook request headers
    console.log("=== REQUEST HEADERS ===");
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
      console.log(`${key}: ${key.toLowerCase() === 'authorization' ? '[REDACTED]' : value}`);
    });

    // Specifically check for stripe-signature header
    const signature = req.headers.get("stripe-signature");
    console.log(`\nStripe signature header present: ${signature ? "YES" : "NO"}`);
    if (signature) {
      console.log(`Stripe signature value: ${signature}`);
      
      // Parse the signature to check its components
      try {
        const components = signature.split(',').reduce((acc, part) => {
          const [key, value] = part.split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
        
        console.log("Signature components:", components);
      } catch (err) {
        console.log("Failed to parse signature components:", err.message);
      }
    }

    // 3. Log the raw request body
    const rawBody = await req.text();
    console.log("\n=== RAW REQUEST BODY ===");
    console.log(rawBody);
    
    // Try to parse the body as JSON for better logging
    try {
      const jsonBody = JSON.parse(rawBody);
      console.log("\n=== PARSED JSON BODY ===");
      console.log("Event type:", jsonBody.type);
      console.log("Event ID:", jsonBody.id);
      console.log("API version:", jsonBody.api_version);
    } catch (err) {
      console.log("Failed to parse body as JSON:", err.message);
    }

    // 4. Attempt signature verification using manual crypto verification
    if (webhookSecret && signature) {
      console.log("\n=== ATTEMPTING SIGNATURE VERIFICATION ===");
      try {
        const verified = await verifyStripeSignature(rawBody, signature, webhookSecret);

        if (verified) {
          console.log("✅ Signature verification SUCCESSFUL!");

          // Parse the verified event
          const event = JSON.parse(rawBody);
          console.log("Verified event type:", event.type);
          console.log("Verified event ID:", event.id);
        } else {
          throw new Error('Signature verification failed');
        }

      } catch (err) {
        console.log("❌ Signature verification FAILED:", err.message);
        console.log("Detailed error:", err);

        // Continue processing despite verification failure
        console.log("Continuing despite verification failure for testing purposes");
      }
    }

    // 5. Return a success response for testing
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Webhook received successfully (TEST MODE - signature verification bypassed)",
        timestamp: new Date().toISOString(),
        headers: headers,
        bodyLength: rawBody.length,
      }),
      { 
        status: 200, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );

  } catch (error) {
    // Log any errors that occurred
    console.error("Error processing webhook:", error);
    
    return new Response(
      JSON.stringify({ 
        error: "Error processing webhook", 
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
});
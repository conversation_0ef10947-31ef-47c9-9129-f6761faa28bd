# Stripe Webhook Test Function

This is a simplified test version of the Stripe webhook handler designed to help debug webhook signature verification issues.

## Purpose

This function helps diagnose why the Stripe CLI is returning 400 "Webhook signature verification failed" errors by:

1. Logging the `STRIPE_WEBHOOK_SECRET` environment variable value (masked for security)
2. Logging all incoming webhook request headers (especially the stripe-signature header)
3. Logging the raw request body to verify payload structure
4. Temporarily bypassing signature verification to isolate the issue
5. Returning a clear response indicating whether the webhook was received successfully

## How to Use

### 1. Test with Stripe CLI

```bash
stripe listen --forward-to https://peuelelfodoiyvmjscfg.supabase.co/functions/v1/stripe-webhook-test
```

### 2. Check Logs

After sending test events, check the Supabase Edge Function logs to see detailed debugging information.

### 3. Common Issues to Look For

- **Missing or incorrect STRIPE_WEBHOOK_SECRET**: The logs will show if the secret is configured and its format
- **Malformed webhook signatures**: The logs will show the exact signature header received
- **Request body parsing issues**: The logs will show the raw request body
- **Timestamp issues**: The logs will show the timestamp components of the signature

## Troubleshooting Steps

1. Verify the `STRIPE_WEBHOOK_SECRET` is correctly set in Supabase environment variables
2. Ensure the secret matches exactly what's shown in the Stripe dashboard
3. Check for any timestamp discrepancies that might cause signature validation to fail
4. Verify the request body is being properly received and parsed

## After Testing

Once the issue is identified and fixed, you can:

1. Update the main `stripe-webhook` function with the correct configuration
2. Re-enable signature verification
3. Test with the main webhook endpoint

## Security Note

This test function intentionally bypasses signature verification for debugging purposes. It should only be used temporarily during development and testing.
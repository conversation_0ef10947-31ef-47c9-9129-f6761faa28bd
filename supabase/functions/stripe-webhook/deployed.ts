import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@15.6.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import { Resend } from "https://esm.sh/resend@2.1.0";
// Email service class
class EmailService {
  resend;
  supabase;
  fromEmail;
  adminEmail;
  constructor(){
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    this.fromEmail = Deno.env.get("FROM_EMAIL");
    this.adminEmail = Deno.env.get("ADMIN_EMAIL");
    if (!resendApiKey) {
      throw new Error("RESEND_API_KEY environment variable is required");
    }
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are required");
    }
    this.resend = new Resend(resendApiKey);
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }
  // Log email attempt to database
  async logEmail(emailLog) {
    try {
      const { data, error } = await this.supabase.from('email_logs').insert(emailLog).select('id').single();
      if (error) {
        console.error('Error logging email:', error);
        return null;
      }
      return data?.id || null;
    } catch (error) {
      console.error('Error logging email:', error);
      return null;
    }
  }
  // Update email log status
  async updateEmailLog(logId, updates) {
    try {
      const { error } = await this.supabase.from('email_logs').update(updates).eq('id', logId);
      if (error) {
        console.error('Error updating email log:', error);
      }
    } catch (error) {
      console.error('Error updating email log:', error);
    }
  }
  // Get user email from auth.users table
  async getUserEmail(userId) {
    try {
      const { data, error } = await this.supabase.auth.admin.getUserById(userId);
      if (error || !data?.user?.email) {
        console.error('Error fetching user email:', error);
        return null;
      }
      return data.user.email;
    } catch (error) {
      console.error('Error fetching user email:', error);
      return null;
    }
  }
  // Get certificate and user data
  async getCertificateData(certificateId) {
    try {
      const { data: certificate, error: certError } = await this.supabase.from('energieausweise').select('*').eq('id', certificateId).single();
      if (certError || !certificate) {
        console.error('Error fetching certificate:', certError);
        return null;
      }
      const userEmail = await this.getUserEmail(certificate.user_id);
      if (!userEmail) {
        console.error('Could not fetch user email for certificate:', certificateId);
        return null;
      }
      return {
        certificate,
        userEmail
      };
    } catch (error) {
      console.error('Error fetching certificate data:', error);
      return null;
    }
  }
  // Generate customer success email content
  generateCustomerSuccessEmail(certificate, userEmail) {
    const certificateTypeMap = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };
    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}` : 'Ihre Immobilie';
    const subject = `Zahlungsbestätigung - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsbestätigung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #16a34a; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsbestätigung</h1>
          </div>
          <div class="content">
            <p>Vielen Dank für Ihre Bestellung! Ihre Zahlung wurde erfolgreich verarbeitet.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Energieausweis-Typ:</strong> ${certificateTypeName}</p>
              <p><strong>Immobilie:</strong> ${buildingAddress}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Ihr Energieausweis wird nun erstellt und Sie erhalten ihn in Kürze per E-Mail.</p>

            <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate customer failure email content
  generateCustomerFailureEmail(certificate, userEmail) {
    const subject = `Zahlungsproblem - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsproblem</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          .retry-button { background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsproblem</h1>
          </div>
          <div class="content">
            <p>Leider konnte Ihre Zahlung für den Energieausweis nicht verarbeitet werden.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Mögliche Gründe für das Zahlungsproblem:</p>
            <ul>
              <li>Unzureichende Deckung auf dem Konto</li>
              <li>Falsche Kartendaten</li>
              <li>Technisches Problem bei der Zahlungsabwicklung</li>
            </ul>

            <p>Sie können die Zahlung jederzeit erneut versuchen:</p>
            <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/meine-zertifikate" class="retry-button">Zahlung erneut versuchen</a>

            <p>Bei anhaltenden Problemen kontaktieren Sie uns bitte.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate admin success email content
  generateAdminSuccessEmail(certificate, userEmail) {
    const certificateTypeMap = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };
    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}` : 'Nicht verfügbar';
    const subject = `Neue Bestellung - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Neue Bestellung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1f2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Neue Energieausweis-Bestellung</h1>
          </div>
          <div class="content">
            <p>Eine neue Bestellung ist eingegangen und wurde erfolgreich bezahlt.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Energieausweis-Typ</th><td>${certificateTypeName}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <div class="details">
              <h3>Immobilieninformationen:</h3>
              <table>
                <tr><th>Adresse</th><td>${buildingAddress}</td></tr>
                ${certificate.objektdaten?.baujahr ? `<tr><th>Baujahr</th><td>${certificate.objektdaten.baujahr}</td></tr>` : ''}
                ${certificate.objektdaten?.wohnflaeche ? `<tr><th>Wohnfläche</th><td>${certificate.objektdaten.wohnflaeche} m²</td></tr>` : ''}
                ${certificate.objektdaten?.anzahlWohneinheiten ? `<tr><th>Anzahl Wohneinheiten</th><td>${certificate.objektdaten.anzahlWohneinheiten}</td></tr>` : ''}
              </table>
            </div>

            <p><strong>Nächste Schritte:</strong></p>
            <ul>
              <li>Energieausweis erstellen</li>
              <li>Qualitätsprüfung durchführen</li>
              <li>Energieausweis an Kunden senden</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate admin failure email content
  generateAdminFailureEmail(certificate, userEmail) {
    const subject = `Zahlungsfehlschlag - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsfehlschlag</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsfehlschlag</h1>
          </div>
          <div class="content">
            <p>Eine Zahlung für eine Energieausweis-Bestellung ist fehlgeschlagen.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE')}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <p><strong>Erforderliche Maßnahmen:</strong></p>
            <ul>
              <li>Kunde wurde über den Zahlungsfehlschlag informiert</li>
              <li>Kunde kann Zahlung erneut versuchen</li>
              <li>Bei wiederholten Problemen Kunde kontaktieren</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Send email using Resend
  async sendEmail(to, subject, html) {
    try {
      const { data, error } = await this.resend.emails.send({
        from: this.fromEmail,
        to: [
          to
        ],
        subject,
        html
      });
      if (error) {
        console.error('Resend error:', error);
        return {
          success: false,
          error: error.message || 'Unknown Resend error'
        };
      }
      return {
        success: true,
        messageId: data?.id
      };
    } catch (error) {
      console.error('Email sending error:', error);
      return {
        success: false,
        error: error.message || 'Unknown email sending error'
      };
    }
  }
  // Send customer success notification
  async sendCustomerSuccessNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer success notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerSuccessEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_success',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(userEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send customer success email:', result.error);
        return false;
      }
      console.log(`Customer success email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer success notification:', error);
      return false;
    }
  }
  // Send customer failure notification
  async sendCustomerFailureNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer failure notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerFailureEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_failure',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(userEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send customer failure email:', result.error);
        return false;
      }
      console.log(`Customer failure email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer failure notification:', error);
      return false;
    }
  }
  // Send admin success notification
  async sendAdminSuccessNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin success notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminSuccessEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_success',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send admin success email:', result.error);
        return false;
      }
      console.log(`Admin success email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin success notification:', error);
      return false;
    }
  }
  // Send admin failure notification
  async sendAdminFailureNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin failure notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminFailureEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_failure',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send admin failure email:', result.error);
        return false;
      }
      console.log(`Admin failure email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin failure notification:', error);
      return false;
    }
  }
  // Send all notifications for payment success
  async sendPaymentSuccessNotifications(certificateId) {
    const customerSent = await this.sendCustomerSuccessNotification(certificateId);
    const adminSent = await this.sendAdminSuccessNotification(certificateId);
    return {
      customerSent,
      adminSent
    };
  }
  // Send all notifications for payment failure
  async sendPaymentFailureNotifications(certificateId) {
    const customerSent = await this.sendCustomerFailureNotification(certificateId);
    const adminSent = await this.sendAdminFailureNotification(certificateId);
    return {
      customerSent,
      adminSent
    };
  }
}
// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  // Initialize variables for proper error handling
  let supabase = null;
  let emailService = null;
  let isSignatureVerified = false;
  let event = null;
  try {
    // Validate environment variables first
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    if (!stripeSecretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable is not set");
    }
    if (!webhookSecret) {
      throw new Error("STRIPE_WEBHOOK_SECRET environment variable is not set");
    }
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase credentials");
    }
    // Get signature from headers
    const signature = req.headers.get("stripe-signature");
    console.log(`Stripe signature header present: ${signature ? "YES" : "NO"}`);
    if (!signature) {
      console.error("❌ Missing Stripe signature header");
      return new Response(JSON.stringify({
        error: "Missing Stripe signature",
        message: "Webhook signature verification failed - no signature header found",
        timestamp: new Date().toISOString()
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log(`Stripe signature value: ${signature}`);
    // Parse the signature to check its components for debugging
    try {
      const components = signature.split(',').reduce((acc, part)=>{
        const [key, value] = part.split('=');
        acc[key] = value;
        return acc;
      }, {});
      console.log("Signature components:", components);
    } catch (err) {
      console.log("Failed to parse signature components:", err.message);
    }
    // Log webhook secret info for debugging (masked)
    const maskedSecret = webhookSecret.length > 8 ? `${webhookSecret.substring(0, 4)}...${webhookSecret.substring(webhookSecret.length - 4)}` : "(too short to mask safely)";
    console.log(`STRIPE_WEBHOOK_SECRET format: ${maskedSecret}`);
    console.log(`STRIPE_WEBHOOK_SECRET length: ${webhookSecret.length} characters`);
    // Initialize Supabase client
    supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Initialize email service (optional - don't fail if this fails)
    try {
      emailService = new EmailService();
      console.log("✅ Email service initialized successfully");
    } catch (emailError) {
      console.warn('⚠️ Email service initialization failed:', emailError);
    // Continue without email service - webhook should still work for payment processing
    }
    // Get the raw body for signature verification
    const body = await req.text();
    console.log("\n=== RAW REQUEST BODY ===");
    console.log(body.substring(0, 500) + (body.length > 500 ? "... (truncated)" : ""));
    // Try to parse the body as JSON for better logging (but don't use parsed version for verification)
    try {
      const jsonBody = JSON.parse(body);
      console.log("\n=== PARSED JSON BODY ===");
      console.log("Event type:", jsonBody.type);
      console.log("Event ID:", jsonBody.id);
      console.log("API version:", jsonBody.api_version);
    } catch (err) {
      console.log("Failed to parse body as JSON for logging:", err.message);
    }
    // Initialize Stripe with secret key for webhook verification
    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2024-11-20'
    });
    // Create SubtleCryptoProvider for async webhook verification in Deno
    const cryptoProvider = Stripe.createSubtleCryptoProvider();
    // CRITICAL: Verify webhook signature using Stripe's async built-in method
    console.log("\n=== ATTEMPTING SIGNATURE VERIFICATION ===");
    try {
      event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret, undefined, cryptoProvider);
      // Mark signature as verified ONLY after successful verification
      isSignatureVerified = true;
      console.log("✅ Signature verification SUCCESSFUL!");
      console.log("Verified event type:", event.type);
      console.log("Verified event ID:", event.id);
    } catch (err) {
      console.error(`❌ Webhook signature verification failed: ${err.message}`);
      console.error("Detailed error:", err);
      // Log additional debugging information
      console.error("Request headers:", Object.fromEntries(req.headers.entries()));
      console.error("Body length:", body.length);
      console.error("Body type:", typeof body);
      // SECURITY: Return error immediately - do NOT process payment without verification
      return new Response(JSON.stringify({
        error: "Webhook signature verification failed",
        message: err.message,
        details: "Payment processing aborted due to failed signature verification",
        timestamp: new Date().toISOString()
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // SECURITY CHECK: Only proceed if signature verification succeeded
    if (!isSignatureVerified || !event) {
      console.error("❌ SECURITY VIOLATION: Attempting to process webhook without verified signature");
      return new Response(JSON.stringify({
        error: "Security violation",
        message: "Webhook processing requires verified signature",
        timestamp: new Date().toISOString()
      }), {
        status: 403,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log(`✅ Processing verified event: ${event.type}`);
    // Handle the verified event - ONLY process payments with verified signatures
    switch(event.type){
      case 'checkout.session.completed':
        {
          const session = event.data.object;
          console.log(`✅ Payment completed for session: ${session.id}`);
          console.log(`Client reference ID: ${session.client_reference_id}`);
          // Validate session data before processing
          if (!session.client_reference_id) {
            console.warn('⚠️ No client_reference_id found in session - cannot update payment status');
            break;
          }
          // Additional validation: check if payment was actually successful
          if (session.payment_status !== 'paid') {
            console.warn(`⚠️ Session payment status is '${session.payment_status}', not 'paid' - skipping payment update`);
            break;
          }
          try {
            // First, get the current certificate to check if order_number exists
            const { data: currentCert, error: fetchError } = await supabase.from('energieausweise').select('order_number, payment_status').eq('id', session.client_reference_id).single();
            if (fetchError) {
              console.error('❌ Error fetching certificate:', fetchError);
              throw new Error(`Failed to fetch certificate: ${fetchError.message}`);
            }
            // Check if payment was already processed to prevent double processing
            if (currentCert?.payment_status === 'paid') {
              console.log(`⚠️ Certificate ${session.client_reference_id} is already marked as paid - skipping duplicate processing`);
              break;
            }
            // Generate order_number if it doesn't exist
            const orderNumber = currentCert?.order_number || `EA-${session.client_reference_id.slice(-8).toUpperCase()}`;
            // Update payment status in database - ONLY after signature verification
            const { error: updateError } = await supabase.from('energieausweise').update({
              payment_status: 'paid',
              stripe_checkout_session_id: session.id,
              order_number: orderNumber,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (updateError) {
              console.error('❌ Error updating payment status:', updateError);
              throw new Error(`Failed to update payment status: ${updateError.message}`);
            }
            console.log(`✅ Successfully updated payment status for certificate: ${session.client_reference_id}`);
            console.log(`✅ Order number set to: ${orderNumber}`);
            // Send email notifications (non-critical - don't fail webhook if this fails)
            if (emailService) {
              try {
                console.log('📧 Sending payment success email notifications...');
                const emailResults = await emailService.sendPaymentSuccessNotifications(session.client_reference_id);
                console.log('📧 Email notification results:', emailResults);
                if (!emailResults.customerSent) {
                  console.warn('⚠️ Failed to send customer success email');
                }
                if (!emailResults.adminSent) {
                  console.warn('⚠️ Failed to send admin success email');
                }
              } catch (emailError) {
                console.error('❌ Error sending email notifications:', emailError);
              // Don't fail the webhook if email sending fails - payment was already processed
              }
            } else {
              console.warn('⚠️ Email service not available - skipping email notifications');
            }
          } catch (paymentError) {
            console.error('❌ Error processing payment completion:', paymentError);
            // Re-throw to trigger webhook retry by Stripe
            throw paymentError;
          }
          break;
        }
      case 'checkout.session.expired':
        {
          const session = event.data.object;
          console.log(`⚠️ Payment session expired: ${session.id}`);
          // Validate session data before processing
          if (!session.client_reference_id) {
            console.warn('⚠️ No client_reference_id found in expired session - cannot update payment status');
            break;
          }
          try {
            // Update payment status to expired
            const { error: updateError } = await supabase.from('energieausweise').update({
              payment_status: 'expired',
              stripe_checkout_session_id: session.id,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (updateError) {
              console.error('❌ Error updating payment status to expired:', updateError);
              throw new Error(`Failed to update payment status to expired: ${updateError.message}`);
            }
            console.log(`✅ Updated payment status to expired for certificate: ${session.client_reference_id}`);
            // Send email notifications for expired session (non-critical)
            if (emailService) {
              try {
                console.log('📧 Sending payment failure email notifications for expired session...');
                const emailResults = await emailService.sendPaymentFailureNotifications(session.client_reference_id);
                console.log('📧 Email notification results for expired session:', emailResults);
                if (!emailResults.customerSent) {
                  console.warn('⚠️ Failed to send customer failure email for expired session');
                }
                if (!emailResults.adminSent) {
                  console.warn('⚠️ Failed to send admin failure email for expired session');
                }
              } catch (emailError) {
                console.error('❌ Error sending email notifications for expired session:', emailError);
              // Don't fail the webhook if email sending fails
              }
            } else {
              console.warn('⚠️ Email service not available - skipping email notifications for expired session');
            }
          } catch (expiredError) {
            console.error('❌ Error processing expired session:', expiredError);
            // Re-throw to trigger webhook retry by Stripe
            throw expiredError;
          }
          break;
        }
      case 'payment_intent.succeeded':
        {
          const paymentIntent = event.data.object;
          console.log(`✅ Payment intent succeeded: ${paymentIntent.id}`);
          break;
        }
      case 'payment_intent.payment_failed':
        {
          const paymentIntent = event.data.object;
          console.log(`❌ Payment intent failed: ${paymentIntent.id}`);
          // Try to find the certificate ID from metadata
          const certificateId = paymentIntent.metadata?.certificate_id;
          if (!certificateId) {
            console.warn('⚠️ No certificate_id found in payment intent metadata - cannot send failure notifications');
            break;
          }
          // Send failure notifications (non-critical)
          if (emailService) {
            try {
              console.log('📧 Sending payment failure email notifications for failed payment intent...');
              const emailResults = await emailService.sendPaymentFailureNotifications(certificateId);
              console.log('📧 Email notification results for failed payment intent:', emailResults);
              if (!emailResults.customerSent) {
                console.warn('⚠️ Failed to send customer failure email for failed payment intent');
              }
              if (!emailResults.adminSent) {
                console.warn('⚠️ Failed to send admin failure email for failed payment intent');
              }
            } catch (emailError) {
              console.error('❌ Error sending email notifications for failed payment intent:', emailError);
            // Don't fail the webhook if email sending fails
            }
          } else {
            console.warn('⚠️ Email service not available - skipping email notifications for failed payment intent');
          }
          break;
        }
      default:
        console.log(`ℹ️ Unhandled event type: ${event.type} - ignoring`);
    }
    // Success response - webhook processed successfully
    console.log(`✅ Webhook processed successfully for event: ${event.type}`);
    return new Response(JSON.stringify({
      received: true,
      event_type: event.type,
      event_id: event.id,
      processed_at: new Date().toISOString()
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error("❌ Error processing webhook:", error);
    // Determine if this is a signature verification error or processing error
    const isSignatureError = error.message?.includes('signature') || error.message?.includes('verification') || !isSignatureVerified;
    const statusCode = isSignatureError ? 400 : 500;
    const errorType = isSignatureError ? 'Signature Verification Error' : 'Webhook Processing Error';
    console.error(`Error type: ${errorType}, Status: ${statusCode}`);
    return new Response(JSON.stringify({
      error: errorType,
      message: error.message,
      details: isSignatureError ? "Webhook signature verification failed - request rejected for security" : "Internal error processing verified webhook - Stripe will retry",
      signature_verified: isSignatureVerified,
      event_type: event?.type || 'unknown',
      timestamp: new Date().toISOString(),
      // Include stack trace only for processing errors (not signature errors)
      ...isSignatureError ? {} : {
        stack: error.stack
      }
    }), {
      status: statusCode,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});

import { useState, useEffect, useCallback } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { ActiveCertificateIndicator } from '../../components/ui/ActiveCertificateIndicator';
import { RadioField } from '../../components/ui/RadioField';

// Define the certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define the form schema using Zod
const objektdatenSchema = z.object({
  // Objektdaten
  ID: z.string().min(1, 'ID ist erforderlich'),
  Strasse: z.string().min(1, 'Straße ist erforderlich'),
  Hausnr: z.string().min(1, 'Hausnummer ist erforderlich'),
  PLZ: z.string().length(5, 'PLZ muss 5 Ziffern haben').regex(/^\d+$/, 'PLZ muss aus Ziffern bestehen'),
  Ort: z.string().min(1, 'Ort ist erforderlich'),
  WSchVo77_erfuellt: z.string().optional(),
  gebaeudebild: z.string().optional(),

  // Kundendaten
  Kunden_Anrede: z.string().min(1, 'Anrede ist erforderlich'),
  Kunden_Vorname: z.string().min(1, 'Vorname ist erforderlich'),
  Kunden_Nachname: z.string().min(1, 'Nachname ist erforderlich'),
  Kunden_Strasse: z.string().min(1, 'Straße ist erforderlich'),
  Kunden_Hausnr: z.string().min(1, 'Hausnummer ist erforderlich'),
  Kunden_PLZ: z.string().length(5, 'PLZ muss 5 Ziffern haben').regex(/^\d+$/, 'PLZ muss aus Ziffern bestehen'),
  Kunden_Ort: z.string().min(1, 'Ort ist erforderlich'),
  Kunden_email: z.string().email('Gültige E-Mail-Adresse erforderlich'),
  Kunden_telefon: z.string().optional(),
});

type ObjektdatenFormValues = z.infer<typeof objektdatenSchema>;

export const ObjektdatenPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const { activeCertificateId } = useCertificate();

  // Function to generate a unique human-readable ID based on address information
  const generateUniqueId = useCallback((strasse: string, hausnr: string, plz: string, ort: string) => {
    if (!strasse || !hausnr || !plz || !ort) return '';

    // Create a base ID from the first 3 characters of the street name (uppercase)
    // followed by the house number, postal code, and first 3 characters of the city
    const streetPrefix = strasse.substring(0, 3).toUpperCase();
    const cityPrefix = ort.substring(0, 3).toUpperCase();

    // Add a timestamp to ensure uniqueness
    const timestamp = new Date().getTime().toString().substring(9, 13);

    return `${streetPrefix}-${hausnr}-${plz.substring(0, 3)}-${cityPrefix}-${timestamp}`;
  }, []);

  const [initialValues, setInitialValues] = useState<Partial<ObjektdatenFormValues>>({
    ID: '',
    Strasse: '',
    Hausnr: '',
    PLZ: '',
    Ort: '',
    WSchVo77_erfuellt: '1', // Default to "Ja"
    gebaeudebild: '',
    Kunden_Anrede: '',
    Kunden_Vorname: '',
    Kunden_Nachname: '',
    Kunden_Strasse: '',
    Kunden_Hausnr: '',
    Kunden_PLZ: '',
    Kunden_Ort: '',
    Kunden_email: '',
    Kunden_telefon: '',
  });

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Fetch existing objektdaten
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'objektdaten', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      // Use proper typing for the Supabase query builder
      const { data, error } = await supabase
        .from('energieausweise')
        .select('objektdaten')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      // `data` will be an object like { objektdaten: YourData } or null if no row found
      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as CertificateType);
    }
  }, [certificateData]);

  useEffect(() => {
    // `existingData` can be null (if no row found) or an object { objektdaten: ... }
    if (existingData && typeof existingData === 'object' && 'objektdaten' in existingData && existingData.objektdaten) {
      setInitialValues(prev => ({
        ...prev,
        ...(existingData.objektdaten as ObjektdatenFormValues) // Ensure type safety
      }));
    }
    setIsLoading(false);
  }, [existingData]);

  const saveMutation = useMutation({
    mutationFn: async (formData: ObjektdatenFormValues) => { // Renamed 'data' to 'formData' for clarity
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // Create a copy of the form data to modify
      const dataToSave = { ...formData };

      // Remove WSchVo77_erfuellt field if certificate type is not WG/V
      if (certificateType !== 'WG/V') {
        delete dataToSave.WSchVo77_erfuellt;
      }

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          objektdaten: dataToSave, // Use the modified form data
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select() // it's good practice to select to get the written data back, if needed
        .single(); // if you expect one row back from upsert

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'objektdaten', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      navigate({ to: '/erfassen/gebaeudedetails1' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      // Ensure the value conforms to ObjektdatenFormValues, Zod validation would typically handle this upstream
      // or you can add validation here.
      try {
        const validatedValues = objektdatenSchema.parse(value);
        saveMutation.mutate(validatedValues);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          // Handle Zod validation errors, e.g., by setting them in the form state
          // For now, logging or setting a general submit error
          console.error("Form validation error:", validationError.flatten().fieldErrors);
          setSubmitError("Validierungsfehler im Formular. Bitte überprüfen Sie Ihre Eingaben.");
          // You might want to map these errors to your form fields' error states.
        } else {
          setSubmitError("Ein unerwarteter Validierungsfehler ist aufgetreten.");
        }
      }
    },
    // It's good practice to enable validation with Zod here
    // validatorAdapter: zodValidator(), // If using @tanstack/zod-form-adapter
    // zodValidator: objektdatenSchema, // Or however your form library integrates Zod
  });

  // Enable reinitialization of form when initialValues change (e.g., after data loads)
  useEffect(() => {
    form.reset(initialValues);
  }, [initialValues, form]);

  // Generate ID when address fields change
  useEffect(() => {
    // Get current form values
    const formValues = form.state.values;
    const strasse = formValues.Strasse as string;
    const hausnr = formValues.Hausnr as string;
    const plz = formValues.PLZ as string;
    const ort = formValues.Ort as string;

    // Only generate ID if all required fields are filled and ID is empty
    if (strasse && hausnr && plz && ort && (!formValues.ID || formValues.ID === '')) {
      const newId = generateUniqueId(strasse, hausnr, plz, ort);
      if (newId) {
        form.setFieldValue('ID', newId);
      }
    }
  }, [form.state.values.Strasse, form.state.values.Hausnr, form.state.values.PLZ, form.state.values.Ort, generateUniqueId, form]);

  // Handle file upload for building image
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setUploadedFile(file);

    // Upload file to Supabase Storage
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      const userId = user.user.id;
      const filePath = `${userId}/${Date.now()}_${file.name}`;

      // Create a custom upload with progress tracking
      const xhr = new XMLHttpRequest();

      // Set up progress tracking
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(percent);
        }
      });

      // Create a promise to handle the upload
      const uploadPromise = new Promise<{ data: any; error: any }>((resolve) => {
        xhr.onreadystatechange = function() {
          if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve({ data: true, error: null });
            } else {
              resolve({ data: null, error: new Error(`Upload failed with status ${xhr.status}`) });
            }
          }
        };
      });

      // Get the presigned URL for upload
      const { data: uploadData } = await supabase.storage
        .from('gebaeudebilder')
        .createSignedUploadUrl(filePath);

      if (!uploadData) {
        throw new Error('Fehler beim Erstellen der Upload-URL');
      }

      // Configure the request
      xhr.open('PUT', uploadData.signedUrl);

      // Send the file
      xhr.send(file);

      // Wait for the upload to complete
      const { error } = await uploadPromise;

      if (error) throw error;

      // Store the file path in the database instead of the public URL
      // We'll generate signed URLs on demand when the user wants to view the image
      const fullPath = `${supabase.storage.from('gebaeudebilder').getPublicUrl(filePath).data.publicUrl}`;

      // Update form field with the file path
      form.setFieldValue('gebaeudebild', fullPath);

    } catch (error) {
      console.error('Fehler beim Hochladen:', error);
      setSubmitError(`Fehler beim Hochladen der Datei: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    }
  };


  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = true
  }: {
    name: keyof ObjektdatenFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const field = useField({
      form,
      name: name as any, // Using 'any' here can be avoided with more specific form typing
       // Add validators from Zod schema if desired for live validation
      // validators: {
      //   onChange: objektdatenSchema.shape[name] ? z.object({ [name]: objektdatenSchema.shape[name] }) : undefined,
      // }
    });

    return (
      <div className="mb-4">
        <label htmlFor={field.name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={field.name}
          name={field.name}
          type={type}
          value={field.state.value ?? ''}
          onChange={(e) => field.handleChange(e.target.value)}
          onBlur={field.handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            field.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {field.state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{field.state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // File upload field component
  // Read-only field component for displaying the generated ID
  const ReadOnlyIdField = () => {
    const field = useField({
      form,
      name: 'ID',
    });

    return (
      <div className="mb-4">
        <label htmlFor="ID" className="block text-sm font-medium text-gray-700 mb-1">
          Gebäude-ID <span className="text-red-500">*</span>
        </label>
        <div className="flex items-center">
          <input
            id="ID"
            name="ID"
            type="text"
            value={field.state.value ?? ''}
            readOnly
            className="w-full px-3 py-2 border border-gray-300 bg-gray-50 rounded-md shadow-sm focus:outline-none"
          />
          {field.state.value && (
            <div className="ml-2 text-sm text-gray-600">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Automatisch generiert</span>
            </div>
          )}
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Eine eindeutige ID wird automatisch aus den Adressdaten generiert
        </p>
      </div>
    );
  };

  const FileUploadField = ({
    label,
    accept = 'image/*',
    required = false
  }: {
    label: string;
    accept?: string;
    required?: boolean;
  }) => {
    const { state } = useField({
      name: 'gebaeudebild',
      form,
    });

    const progress = uploadProgress;
    const file = uploadedFile;
    const [signedUrl, setSignedUrl] = useState<string | null>(null);

    // Function to get a signed URL for the image
    const getSignedUrl = useCallback(async (path: string) => {
      try {
        // Extract the file path from the full URL
        // The URL format is typically: https://[project-ref].supabase.co/storage/v1/object/public/[bucket]/[path]
        const urlParts = path.split('/');
        const bucketIndex = urlParts.findIndex(part => part === 'gebaeudebilder');

        if (bucketIndex === -1) {
          console.error('Invalid file path format:', path);
          return null;
        }

        // Extract the file path (everything after the bucket name)
        const filePath = urlParts.slice(bucketIndex + 1).join('/');

        // Get a signed URL that will work for 60 minutes (3600 seconds)
        const { data, error } = await supabase.storage
          .from('gebaeudebilder')
          .createSignedUrl(filePath, 3600);

        if (error) {
          console.error('Error creating signed URL:', error);
          return null;
        }

        return data.signedUrl;
      } catch (error) {
        console.error('Error in getSignedUrl:', error);
        return null;
      }
    }, []);

    // Get a signed URL when the component mounts or when the state.value changes
    useEffect(() => {
      if (state.value && typeof state.value === 'string') {
        getSignedUrl(state.value).then(url => {
          if (url) setSignedUrl(url);
        });
      }
    }, [state.value, getSignedUrl]);

    return (
      <div className="mb-4">
        <label htmlFor="gebaeudebild" className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>

        <div className="flex items-center space-x-2">
          <input
            type="file"
            id="gebaeudebild"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
          />
          <label
            htmlFor="gebaeudebild"
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors cursor-pointer"
          >
            Datei auswählen
          </label>

          {file && (
            <span className="text-sm text-gray-600">
              {file.name} ({Math.round(file.size / 1024)} KB)
            </span>
          )}

          {state.value && !file && signedUrl && (
            <a
              href={signedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-green-600 hover:underline"
            >
              Hochgeladenes Bild anzeigen
            </a>
          )}
        </div>

        {progress > 0 && progress < 100 && (
          <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
            <div
              className="bg-green-600 h-2.5 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        )}

        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Objektdaten erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die allgemeinen Objektdaten und Kundendaten ein.
      </p>

      <ActiveCertificateIndicator />

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Objektdaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <ReadOnlyIdField />
            </div>
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <FormField name="Strasse" label="Straße" placeholder="Straße des Objektes" />
              </div>
              <FormField name="Hausnr" label="Hausnummer" placeholder="222" />
            </div>
            <FormField name="PLZ" label="PLZ" placeholder="99423" />
            <FormField name="Ort" label="Ort" placeholder="Weimar" />
            {certificateType === 'WG/V' && (
              <RadioField
                name="WSchVo77_erfuellt"
                label="Erfüllt das Gebäude die Wärmeschutzverordnung von 1977?"
                form={form}
              />
            )}
            <div className="md:col-span-2">
              <FileUploadField
                label="Gebäudebild hochladen"
                accept="image/*"
              />
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Kundendaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField name="Kunden_Anrede" label="Anrede" placeholder="Herr/Frau" />
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField name="Kunden_Vorname" label="Vorname" placeholder="Max" />
              <FormField name="Kunden_Nachname" label="Nachname" placeholder="Mustermann" />
            </div>
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <FormField name="Kunden_Strasse" label="Straße" placeholder="Musterstr." />
              </div>
              <FormField name="Kunden_Hausnr" label="Hausnummer" placeholder="111" />
            </div>
            <FormField name="Kunden_PLZ" label="PLZ" placeholder="99425" />
            <FormField name="Kunden_Ort" label="Ort" placeholder="Weimar" />
            <FormField name="Kunden_email" label="E-Mail" type="email" placeholder="<EMAIL>" />
            <FormField name="Kunden_telefon" label="Telefon" placeholder="03643/" required={false} />
          </div>
        </div>


        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        <div className="flex justify-between mt-8">
          <Link
            to="/erfassen"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
          </button>
        </div>
      </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};
